import tkinter as tk
from tkinter import ttk
from datetime import datetime

class TallyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Apartment Tally Program")
        self.root.geometry("800x600")

        # --- Sorting Logic ---
        initial_flats = [
            "WH Flat 3", "WH Flat 4", "LH Flat 1", "LH Flat 3", "LH Flat 4",
            "LH Flat 5", "LH Flat 6", "LH Flat 7", "LH Flat 9"
        ]
        
        # Custom sort key function
        def sort_key(flat_name):
            parts = flat_name.split()
            building = parts[0]
            number = int(parts[2])
            return (building, -number) # Sort by building, then descending number

        sorted_flat_names = sorted(initial_flats, key=sort_key)

        self.flats = {name: {"status": "In", "last_action_time": None} for name in sorted_flat_names}

        # --- Main scrollable setup ---
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=1)

        my_canvas = tk.Canvas(main_frame)
        my_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=1)

        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=my_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        my_canvas.configure(yscrollcommand=scrollbar.set)
        my_canvas.bind('<Configure>', lambda e: my_canvas.configure(scrollregion=my_canvas.bbox("all")))

        self.scrollable_frame = ttk.Frame(my_canvas)
        my_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        self.create_widgets()

    def create_widgets(self):
        for name in self.flats:
            pane = CollapsiblePane(self.scrollable_frame, name)
            pane.pack(pady=5, padx=10, fill="x")

            # --- Flat Information and Action Button ---
            top_frame = ttk.Frame(pane.frame)
            top_frame.pack(fill=tk.X, padx=5, pady=5)

            button = tk.Button(top_frame, text="Leave", command=lambda n=name: self.log_time(n), bg="green", fg="white", width=10)
            button.pack(side=tk.LEFT, padx=(0,10))
            self.flats[name]["button"] = button

            # --- Notes Entry ---
            notes_label = ttk.Label(top_frame, text="Notes:")
            notes_label.pack(side=tk.LEFT)
            notes_entry = tk.Entry(top_frame)
            notes_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
            self.flats[name]["notes_entry"] = notes_entry

            # --- Log Tree ---
            log_frame = ttk.Frame(pane.frame)
            log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0,5))
            
            log_tree = ttk.Treeview(log_frame, columns=("Action", "Date", "Time", "Duration", "Notes"), show="headings", height=4)
            log_tree.heading("Action", text="Action")
            log_tree.heading("Date", text="Date")
            log_tree.heading("Time", text="Time")
            log_tree.heading("Duration", text="Duration")
            log_tree.heading("Notes", text="Notes")

            log_tree.column("Action", width=80, anchor='center')
            log_tree.column("Date", width=100, anchor='center')
            log_tree.column("Time", width=100, anchor='center')
            log_tree.column("Duration", width=100, anchor='center')
            log_tree.column("Notes", width=300)

            log_tree.pack(fill=tk.BOTH, expand=True)
            log_tree.bind("<<TreeviewSelect>>", lambda event, tree=log_tree: self.on_tree_select(event, tree))
            self.flats[name]["log_tree"] = log_tree

    def on_tree_select(self, event, tree):
        try:
            selected_item = tree.selection()[0]
            time_val = tree.item(selected_item)['values'][2]
            self.root.clipboard_clear()
            self.root.clipboard_append(time_val)
        except IndexError:
            pass

    def log_time(self, name):
        flat = self.flats[name]
        current_time = datetime.now()
        date_str = current_time.strftime("%Y-%m-%d")
        # --- MODIFIED LINE: Changed time format to remove seconds ---
        time_str = current_time.strftime("%H:%M") 
        notes = flat["notes_entry"].get()

        if flat["status"] == "In":
            flat["status"] = "Out"
            flat["button"].config(text="Return", bg="red")
            action = "Left"
            flat["last_action_time"] = current_time
            flat["log_tree"].insert("", tk.END, values=(action, date_str, time_str, "", notes))
        else:
            flat["status"] = "In"
            flat["button"].config(text="Leave", bg="green")
            action = "Returned"
            duration = ""
            if flat["last_action_time"]:
                # The duration calculation remains precise
                time_difference = current_time - flat["last_action_time"]
                duration = str(time_difference).split('.')[0]
            flat["log_tree"].insert("", tk.END, values=(action, date_str, time_str, duration, notes))
            flat["last_action_time"] = None

        flat["notes_entry"].delete(0, tk.END)

class CollapsiblePane(ttk.Frame):
    def __init__(self, parent, text=""):
        super().__init__(parent, relief="groove", borderwidth=2)
        
        self.parent = parent
        self._is_collapsed = tk.BooleanVar(value=True)

        self.button = ttk.Button(self, text=f"+ {text}", command=self.toggle, style="Toolbutton")
        self.button.pack(fill="x")
        
        self.frame = ttk.Frame(self)

    def toggle(self):
        if self._is_collapsed.get():
            self.frame.pack(fill="x", padx=2, pady=2)
            self.button.configure(text=self.button.cget("text").replace("+", "-", 1))
            self._is_collapsed.set(False)
        else:
            self.frame.pack_forget()
            self.button.configure(text=self.button.cget("text").replace("-", "+", 1))
            self._is_collapsed.set(True)

if __name__ == "__main__":
    root = tk.Tk()
    app = TallyApp(root)
    root.mainloop()